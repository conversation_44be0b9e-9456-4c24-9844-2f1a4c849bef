<template>
  <div class="map-container">
    <!-- 返回按钮，仅在非全国视图时显示 -->
    <button v-if="mapHistory.length > 1" @click="handleBack" class="back-button">
      返回
    </button>
    <!-- ECharts 图表容器 -->
    <v-chart
      ref="mapChart"
      class="chart"
      :option="chartOption"
      autoresize
      @click="handleMapClick"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, shallowRef } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { MapChart, HeatmapChart, EffectScatterChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  VisualMapComponent,
  GeoComponent,
} from 'echarts/components';
import VChart, { THEME_KEY } from 'vue-echarts';
import { provinceMap } from '../mapMapping.js'; // 确保路径正确

// 按需引入 ECharts 模块以优化打包体积
use([
  Canvas<PERSON><PERSON><PERSON>,
  MapChart,
  HeatmapChart,
  EffectScatterChart,
  TitleComponent,
  TooltipComponent,
  VisualMapComponent,
  GeoComponent,
]);

// ---- 响应式状态定义 ----

const mapChart = ref(null); // 图表实例的引用
const chartOption = ref({}); // ECharts 配置项
const mapHistory = shallowRef(['china']); // 地图钻取历史记录，使用 shallowRef 优化性能

// ---- 模拟数据生成 ----

/**
 * 生成用于省份颜色渲染的模拟数据
 * @returns {Array<{name: string, value: number}>}
 */
const generateProvinceData = () => {
  return Object.keys(provinceMap).map((provinceName) => ({
    name: provinceName,
    value: Math.round(Math.random() * 1500), // 0-1500 的随机热力值
  }));
};

/**
 * 生成用于热力图层的模拟数据
 * @returns {Array<[number, number, number]>} 格式: [经度, 纬度, 热力值]
 */
const generateHeatmapData = () => {
  // 在中国的主要城市附近生成一些基础热点
  const majorCities = [
    [116.46, 39.92, 250], [121.48, 31.22, 300], // 北京, 上海
    [113.23, 23.16, 180], [104.06, 30.67, 220], // 广州, 成都
    [108.95, 34.27, 190], [114.31, 30.52, 170], // 西安, 武汉
  ];
  // 生成一些随机分布的热点
  const randomPoints = Array.from({ length: 150 }, () => [
    73 + Math.random() * 50, // 经度范围 (覆盖中国)
    18 + Math.random() * 35, // 纬度范围
    Math.random() * 100,
  ]);
  return [...majorCities, ...randomPoints];
};

const provinceData = generateProvinceData();
const heatmapData = generateHeatmapData();

// ---- 核心功能方法 ----

/**
 * 异步加载并注册地图的 GeoJSON 数据
 * @param {string} mapName - 地图名称 ('china' 或省份行政代码)
 */
const loadMapData = async (mapName) => {
  try {
    const response = await fetch(`/map/${mapName}.json`);
    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }
    const mapJson = await response.json();
    VChart.registerMap(mapName, mapJson);
  } catch (error) {
    console.error(`加载地图数据失败: ${mapName}.json`, error);
    // 在生产环境中，可以向用户显示更友好的错误提示
    alert(`加载地图 "${mapName}" 失败，请检查网络或文件是否存在。`);
    throw error; // 抛出错误以中断后续操作
  }
};


/**
 * 更新 ECharts 的配置对象
 * @param {string} mapName - 当前要显示的地图名称
 * @param {string} mapNameZH - 当前地图的中文名称
 */
const updateChart = (mapName, mapNameZH) => {
  chartOption.value = {
    // 标题
    title: {
      text: '中国地图热力分布',
      subtext: `当前视图: ${mapNameZH}`,
      left: 'center',
      textStyle: {
        color: '#333',
        fontSize: 20
      }
    },
    // 提示框
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        // 为不同系列定制提示内容
        if (params.seriesType === 'heatmap') {
            return `经纬度: ${params.value[0].toFixed(2)}, ${params.value[1].toFixed(2)}<br/>热力值: ${params.value[2].toFixed(0)}`;
        }
        if (params.seriesType === 'effectScatter') {
             return `${params.data.name}<br/>数据: ${params.data.value[2]}`;
        }
        return `${params.name}<br/>数据值: ${params.value || '无'}`;
      },
    },
    // 左下角的视觉映射组件，用于省份颜色
    visualMap: {
      min: 0,
      max: 1500,
      left: '20px',
      bottom: '20px',
      text: ['高', '低'],
      calculable: true,
      inRange: {
        color: ['#E0FFFF', '#006EDD'], // 颜色范围：从浅蓝到深蓝
      },
      show: mapName === 'china', // 仅在全国视图下显示
    },
    // 地理坐标系组件，是地图和热力图的载体
    geo: {
      map: mapName,
      roam: true, // 开启鼠标缩放和平移漫游
      label: {
        show: true, // 显示地名标签
        color: '#444',
        fontSize: 10,
      },
      itemStyle: { // 地图区域的多边形 图形样式
        areaColor: '#f9f9f9',
        borderColor: '#bbbbbb',
        borderWidth: 0.8,
      },
      emphasis: { // 高亮状态下的样式
        label: {
          color: '#000',
        },
        itemStyle: {
          areaColor: '#FFD700', // 金黄色高亮
        },
      },
    },
    // 系列列表
    series: [
      // 系列1: 用于显示省份颜色的地图
      {
        type: 'map',
        map: mapName,
        geoIndex: 0, // 关联到第一个 geo 组件
        data: provinceData, // 绑定省份数据
        show: mapName === 'china', // 仅在全国视图显示
      },
      // 系列2: 热力图层
      {
        name: '热力点',
        type: 'heatmap',
        coordinateSystem: 'geo', // 使用地理坐标系
        data: heatmapData,
        pointSize: 6,
        blurSize: 8,
      },
      // 系列3: 特效散点图（可选，用于标记重点城市）
      {
        name: '主要城市',
        type: 'effectScatter',
        coordinateSystem: 'geo',
        data: [
          { name: '北京', value: [116.46, 39.92, 100] },
          { name: '上海', value: [121.48, 31.22, 100] },
          { name: '成都', value: [104.06, 30.67, 100] },
        ],
        symbolSize: 12,
        itemStyle: {
          color: '#FF4500', // 橙红色
        },
        showEffectOn: 'render',
        rippleEffect: {
          brushType: 'stroke',
        },
      },
    ],
  };
};

/**
 * 处理地图点击事件，实现下钻
 * @param {object} params - ECharts 点击事件的回调参数
 */
const handleMapClick = async (params) => {
  // 只处理在 geo 组件上且是省份的点击
  if (params.componentType === 'geo' && params.name) {
    const provinceName = params.name;
    const mapCode = provinceMap[provinceName];

    // 检查是否是有效的、可下钻的省份
    if (mapCode) {
      try {
        await loadMapData(mapCode);
        
        // 更新历史记录
        const newHistory = [...mapHistory.value, mapCode];
        mapHistory.value = newHistory; // 使用 shallowRef，必须替换整个数组
        
        updateChart(mapCode, provinceName);
      } catch (error) {
        // loadMapData 内部已经处理了 alert，这里只在控制台记录
        console.error('下钻失败:', error);
      }
    }
  }
};

/**
 * 处理返回按钮的点击事件
 */
const handleBack = async () => {
  if (mapHistory.value.length > 1) {
    const newHistory = mapHistory.value.slice(0, -1);
    mapHistory.value = newHistory;
    
    const previousMapName = newHistory[newHistory.length - 1];
    const previousMapNameZH = previousMapName === 'china' ? '全国' : Object.keys(provinceMap).find(key => provinceMap[key] === previousMapName);

    updateChart(previousMapName, previousMapNameZH);
  }
};

// ---- Vue 生命周期钩子 ----
onMounted(async () => {
  try {
    await loadMapData('china');
    updateChart('china', '全国');
  } catch (error) {
     console.error("初始化中国地图失败:", error);
  }
});

</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 85vh; /* 定义一个较大的高度 */
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden; /* 保证子元素不会溢出圆角 */
}

.chart {
  width: 100%;
  height: 100%;
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  padding: 8px 16px;
  font-size: 14px;
  color: #333;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #ccc;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.back-button:hover {
  background-color: #fff;
  border-color: #aaa;
  transform: translateY(-1px);
}
</style>
