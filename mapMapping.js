// 省份名称到地图代码的映射配置
export const provinceMap = {
  '北京市': 'beijing',
  '天津市': 'tianjin',
  '河北省': 'hebei',
  '山西省': 'shan<PERSON>',
  '内蒙古自治区': 'neimenggu',
  '辽宁省': 'liaoning',
  '吉林省': 'jilin',
  '黑龙江省': 'heilongjiang',
  '上海市': 'shanghai',
  '江苏省': 'jiangsu',
  '浙江省': 'zhejiang',
  '安徽省': 'anhui',
  '福建省': 'fujian',
  '江西省': 'jiangxi',
  '山东省': 'shandong',
  '河南省': 'henan',
  '湖北省': 'hubei',
  '湖南省': 'hunan',
  '广东省': 'guangdong',
  '广西壮族自治区': 'guangxi',
  '海南省': 'hainan',
  '重庆市': 'chongqing',
  '四川省': 'sichuan',
  '贵州省': 'guizhou',
  '云南省': 'yunnan',
  '西藏自治区': 'xizang',
  '陕西省': 'shaanxi',
  '甘肃省': 'gansu',
  '青海省': 'qinghai',
  '宁夏回族自治区': 'ningxia',
  '新疆维吾尔自治区': 'xinjiang',
  '台湾省': 'taiwan',
  '香港特别行政区': 'xianggang',
  '澳门特别行政区': 'aomen'
};

// 地图层级配置
export const mapLevels = {
  COUNTRY: 'country',
  PROVINCE: 'province', 
  CITY: 'city',
  COUNTY: 'county'
};

// 地图数据文件路径配置
export const mapDataPaths = {
  china: './中国_省.geojson',
  cities: './中国_市.geojson',
  counties: './中国_县.geojson'
};

// 地图显示配置
export const mapConfig = {
  // 默认地图中心点
  center: [104.114129, 37.550339],
  // 默认缩放级别
  zoom: 5,
  // 地图样式配置
  itemStyle: {
    normal: {
      areaColor: '#f9f9f9',
      borderColor: '#bbbbbb',
      borderWidth: 0.8
    },
    emphasis: {
      areaColor: '#FFD700',
      borderColor: '#333',
      borderWidth: 1.2
    }
  },
  // 标签配置
  label: {
    normal: {
      show: true,
      color: '#444',
      fontSize: 10
    },
    emphasis: {
      show: true,
      color: '#000',
      fontSize: 12
    }
  }
};

// 热力图配置
export const heatmapConfig = {
  pointSize: 6,
  blurSize: 8,
  minOpacity: 0.3,
  maxOpacity: 0.8
};

// 特效散点图配置
export const effectScatterConfig = {
  symbolSize: 12,
  showEffectOn: 'render',
  rippleEffect: {
    brushType: 'stroke'
  },
  itemStyle: {
    color: '#FF4500'
  }
};
