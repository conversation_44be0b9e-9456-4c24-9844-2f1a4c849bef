# Vue2 + ECharts 地图下钻功能

基于Vue2和ECharts的中国地图可视化组件，支持省市县三级下钻功能。

## 功能特性

- 🗺️ **多层级下钻**: 支持全国→省份→城市→县区的多层级地图下钻浏览
- 🎯 **热力图展示**: 集成热力图和特效散点图，直观展示数据分布
- 🧭 **面包屑导航**: 提供清晰的导航路径，支持快速跳转到任意层级
- ⚡ **高性能渲染**: 基于ECharts Canvas渲染，支持大数据量流畅交互
- 📱 **响应式设计**: 适配不同屏幕尺寸，支持移动端访问

## 技术栈

- **Vue 2.7.14**: 渐进式JavaScript框架
- **ECharts 5.4.3**: 强大的数据可视化库
- **GeoJSON**: 地理数据格式
- **Vite**: 现代化构建工具

## 项目结构

```
├── App.vue                 # 主应用组件
├── MapDrillDown.vue        # 地图下钻组件
├── mapMapping.js           # 地图配置和映射
├── main.js                 # 应用入口文件
├── index.html              # HTML模板
├── package.json            # 项目配置
├── vite.config.js          # Vite配置
├── 中国_省.geojson          # 省级地图数据
├── 中国_县.geojson          # 县级地图数据
└── README.md               # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

### 3. 构建生产版本

```bash
npm run build
```

### 4. 预览构建结果

```bash
npm run preview
```

## 使用说明

### 基本用法

```vue
<template>
  <div>
    <MapDrillDown />
  </div>
</template>

<script>
import MapDrillDown from './MapDrillDown.vue';

export default {
  components: {
    MapDrillDown
  }
};
</script>
```

### 交互操作

1. **下钻操作**: 点击地图上的省份区域可以下钻到该省份的详细地图
2. **面包屑导航**: 使用左上角的面包屑导航可以快速返回到上级地图
3. **返回按钮**: 点击"返回"按钮可以逐级返回上一层地图
4. **地图操作**: 支持鼠标滚轮缩放和拖拽平移操作
5. **信息提示**: 悬停在地图区域上可以查看详细信息

## 配置说明

### 地图配置 (mapMapping.js)

```javascript
// 省份映射配置
export const provinceMap = {
  '北京市': 'beijing',
  '天津市': 'tianjin',
  // ... 更多省份
};

// 地图样式配置
export const mapConfig = {
  center: [104.114129, 37.550339],
  zoom: 5,
  itemStyle: {
    // 地图样式配置
  }
};
```

### 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| data | Array | [] | 地图数据 |
| config | Object | {} | 地图配置 |
| loading | Boolean | false | 加载状态 |

## 数据格式

### GeoJSON数据结构

```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "properties": {
        "name": "省份名称",
        "gb": "行政区划代码"
      },
      "geometry": {
        "type": "MultiPolygon",
        "coordinates": [...]
      }
    }
  ]
}
```

### 业务数据格式

```javascript
const data = [
  {
    name: '北京市',
    value: 1500
  },
  {
    name: '上海市', 
    value: 1200
  }
];
```

## 浏览器支持

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79

## 开发指南

### 添加新的地图层级

1. 在 `mapMapping.js` 中添加新的映射配置
2. 准备对应的GeoJSON数据文件
3. 在 `MapDrillDown.vue` 中添加相应的处理逻辑

### 自定义样式

可以通过修改 `mapConfig` 中的样式配置来自定义地图外观：

```javascript
export const mapConfig = {
  itemStyle: {
    normal: {
      areaColor: '#f9f9f9',
      borderColor: '#bbbbbb',
      borderWidth: 0.8
    },
    emphasis: {
      areaColor: '#FFD700',
      borderColor: '#333',
      borderWidth: 1.2
    }
  }
};
```

## 常见问题

### Q: 地图数据加载失败怎么办？
A: 请检查GeoJSON文件路径是否正确，确保文件可以正常访问。

### Q: 如何添加自定义数据？
A: 修改 `generateMockData` 方法，替换为真实的业务数据。

### Q: 支持哪些地图投影？
A: 目前使用ECharts默认的墨卡托投影，可以通过ECharts配置修改。

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0 (2024-06-25)
- 初始版本发布
- 支持省市县三级地图下钻
- 集成热力图和特效散点图
- 添加面包屑导航功能
