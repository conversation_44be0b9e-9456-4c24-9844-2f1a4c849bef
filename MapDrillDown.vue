<template>
  <div class="map-container">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-nav">
      <span 
        v-for="(item, index) in breadcrumbs" 
        :key="index"
        class="breadcrumb-item"
        :class="{ active: index === breadcrumbs.length - 1 }"
        @click="handleBreadcrumbClick(index)"
      >
        {{ item.name }}
        <i v-if="index < breadcrumbs.length - 1" class="separator">></i>
      </span>
    </div>

    <!-- 返回按钮 -->
    <button 
      v-if="mapHistory.length > 1" 
      @click="handleBack" 
      class="back-button"
    >
      <i class="icon-back">←</i>
      返回
    </button>

    <!-- 地图容器 -->
    <div 
      ref="mapChart" 
      class="chart"
      @click="handleMapClick"
    ></div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>地图数据加载中...</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-overlay">
      <p>{{ error }}</p>
      <button @click="retryLoad" class="retry-button">重试</button>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { MapChart, HeatmapChart, EffectScatterChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  VisualMapComponent,
  GeoComponent,
} from 'echarts/components';
import { 
  provinceMap, 
  mapLevels, 
  mapDataPaths, 
  mapConfig,
  heatmapConfig,
  effectScatterConfig 
} from './mapMapping.js';

// 注册ECharts组件
echarts.use([
  CanvasRenderer,
  MapChart,
  HeatmapChart,
  EffectScatterChart,
  TitleComponent,
  TooltipComponent,
  VisualMapComponent,
  GeoComponent,
]);

export default {
  name: 'MapDrillDown',
  data() {
    return {
      // 地图实例
      chartInstance: null,
      // 地图历史记录
      mapHistory: [{ code: 'china', name: '全国', level: mapLevels.COUNTRY }],
      // 面包屑导航
      breadcrumbs: [{ code: 'china', name: '全国', level: mapLevels.COUNTRY }],
      // 加载状态
      loading: false,
      // 错误信息
      error: null,
      // 地图数据缓存
      mapDataCache: {},
      // 模拟数据
      provinceData: [],
      heatmapData: [],
      // 当前地图配置
      currentMapCode: 'china',
      currentMapName: '全国'
    };
  },
  mounted() {
    this.initChart();
    this.generateMockData();
    this.loadMapData('china');
  },
  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
  },
  methods: {
    /**
     * 初始化ECharts实例
     */
    initChart() {
      this.chartInstance = echarts.init(this.$refs.mapChart);
      
      // 监听图表点击事件
      this.chartInstance.on('click', this.handleChartClick);
      
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize);
    },

    /**
     * 处理窗口大小变化
     */
    handleResize() {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    },

    /**
     * 生成模拟数据
     */
    generateMockData() {
      // 生成省份数据
      this.provinceData = Object.keys(provinceMap).map((provinceName) => ({
        name: provinceName,
        value: Math.round(Math.random() * 1500)
      }));

      // 生成热力图数据
      const majorCities = [
        [116.46, 39.92, 250], // 北京
        [121.48, 31.22, 300], // 上海
        [113.23, 23.16, 180], // 广州
        [104.06, 30.67, 220], // 成都
        [108.95, 34.27, 190], // 西安
        [114.31, 30.52, 170], // 武汉
      ];
      
      const randomPoints = Array.from({ length: 100 }, () => [
        73 + Math.random() * 50, // 经度范围
        18 + Math.random() * 35, // 纬度范围
        Math.random() * 100,
      ]);
      
      this.heatmapData = [...majorCities, ...randomPoints];
    },

    /**
     * 加载地图数据
     */
    async loadMapData(mapCode) {
      if (this.mapDataCache[mapCode]) {
        this.updateChart(mapCode);
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        let dataPath;
        if (mapCode === 'china') {
          dataPath = mapDataPaths.china;
        } else {
          // 对于省份，使用县级数据并过滤
          dataPath = mapDataPaths.counties;
        }

        const response = await fetch(dataPath);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const mapJson = await response.json();
        
        // 如果是省份级别，需要过滤县级数据
        if (mapCode !== 'china') {
          // 这里可以根据省份代码过滤相应的县级数据
          // 暂时使用完整的县级数据
        }
        
        // 注册地图数据
        echarts.registerMap(mapCode, mapJson);
        this.mapDataCache[mapCode] = mapJson;
        
        this.updateChart(mapCode);
      } catch (error) {
        console.error(`加载地图数据失败: ${mapCode}`, error);
        this.error = `加载地图 "${mapCode}" 失败，请检查网络或文件是否存在。`;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 更新图表配置
     */
    updateChart(mapCode) {
      const isChina = mapCode === 'china';
      const mapName = isChina ? '全国' : this.currentMapName;

      const option = {
        title: {
          text: '中国地图热力分布',
          subtext: `当前视图: ${mapName}`,
          left: 'center',
          textStyle: {
            color: '#333',
            fontSize: 20
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            if (params.seriesType === 'heatmap') {
              return `经纬度: ${params.value[0].toFixed(2)}, ${params.value[1].toFixed(2)}<br/>热力值: ${params.value[2].toFixed(0)}`;
            }
            if (params.seriesType === 'effectScatter') {
              return `${params.data.name}<br/>数据: ${params.data.value[2]}`;
            }
            return `${params.name}<br/>数据值: ${params.value || '无'}`;
          },
        },
        visualMap: {
          min: 0,
          max: 1500,
          left: '20px',
          bottom: '20px',
          text: ['高', '低'],
          calculable: true,
          inRange: {
            color: ['#E0FFFF', '#006EDD']
          },
          show: isChina
        },
        geo: {
          map: mapCode,
          roam: true,
          ...mapConfig.itemStyle,
          label: mapConfig.label
        },
        series: [
          // 省份颜色系列
          {
            type: 'map',
            map: mapCode,
            geoIndex: 0,
            data: isChina ? this.provinceData : [],
            show: isChina
          },
          // 热力图系列
          {
            name: '热力点',
            type: 'heatmap',
            coordinateSystem: 'geo',
            data: this.heatmapData,
            ...heatmapConfig
          },
          // 特效散点图系列
          {
            name: '主要城市',
            type: 'effectScatter',
            coordinateSystem: 'geo',
            data: [
              { name: '北京', value: [116.46, 39.92, 100] },
              { name: '上海', value: [121.48, 31.22, 100] },
              { name: '成都', value: [104.06, 30.67, 100] },
            ],
            ...effectScatterConfig
          }
        ]
      };

      this.chartInstance.setOption(option, true);
    },

    /**
     * 处理图表点击事件
     */
    handleChartClick(params) {
      // 只处理在geo组件上且是省份的点击
      if (params.componentType === 'geo' && params.name) {
        const provinceName = params.name;
        const mapCode = provinceMap[provinceName];

        if (mapCode && this.currentMapCode === 'china') {
          this.drillDown(mapCode, provinceName, mapLevels.PROVINCE);
        }
      }
    },

    /**
     * 下钻到指定地图
     */
    async drillDown(mapCode, mapName, level) {
      try {
        await this.loadMapData(mapCode);
        
        // 更新历史记录
        this.mapHistory.push({ code: mapCode, name: mapName, level });
        this.breadcrumbs.push({ code: mapCode, name: mapName, level });
        
        this.currentMapCode = mapCode;
        this.currentMapName = mapName;
      } catch (error) {
        console.error('下钻失败:', error);
      }
    },

    /**
     * 返回上一级
     */
    handleBack() {
      if (this.mapHistory.length > 1) {
        this.mapHistory.pop();
        this.breadcrumbs.pop();
        
        const previousMap = this.mapHistory[this.mapHistory.length - 1];
        this.currentMapCode = previousMap.code;
        this.currentMapName = previousMap.name;
        
        this.updateChart(previousMap.code);
      }
    },

    /**
     * 处理面包屑点击
     */
    handleBreadcrumbClick(index) {
      if (index < this.breadcrumbs.length - 1) {
        const targetMap = this.breadcrumbs[index];
        
        // 截取历史记录和面包屑
        this.mapHistory = this.mapHistory.slice(0, index + 1);
        this.breadcrumbs = this.breadcrumbs.slice(0, index + 1);
        
        this.currentMapCode = targetMap.code;
        this.currentMapName = targetMap.name;
        
        this.updateChart(targetMap.code);
      }
    },

    /**
     * 重试加载
     */
    retryLoad() {
      this.error = null;
      this.loadMapData(this.currentMapCode);
    }
  }
};
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 85vh;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  background-color: #fff;
}

.breadcrumb-nav {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 8px 12px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 14px;
}

.breadcrumb-item {
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.breadcrumb-item:hover {
  color: #1890ff;
}

.breadcrumb-item.active {
  color: #333;
  font-weight: 500;
}

.separator {
  margin: 0 8px;
  color: #ccc;
  font-style: normal;
}

.back-button {
  position: absolute;
  top: 60px;
  left: 15px;
  z-index: 1000;
  padding: 8px 16px;
  font-size: 14px;
  color: #333;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #ccc;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 4px;
}

.back-button:hover {
  background-color: #fff;
  border-color: #aaa;
  transform: translateY(-1px);
}

.icon-back {
  font-style: normal;
  font-weight: bold;
}

.chart {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-align: center;
  z-index: 2000;
}

.retry-button {
  margin-top: 12px;
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #40a9ff;
}
</style>
