{"name": "@vitejs/plugin-vue2", "version": "2.3.3", "license": "MIT", "author": "<PERSON>", "packageManager": "pnpm@7.33.5", "files": ["dist"], "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "engines": {"node": "^14.18.0 || >= 16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite-plugin-vue2.git", "directory": "packages/plugin-vue"}, "bugs": {"url": "https://github.com/vitejs/vite-plugin-vue2/issues"}, "homepage": "https://github.com/vitejs/vite-plugin-vue2/#readme", "peerDependencies": {"vite": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0", "vue": "^2.7.0-0"}, "devDependencies": {"@types/fs-extra": "^9.0.13", "conventional-changelog-cli": "^2.2.2", "debug": "^4.3.4", "enquirer": "^2.3.6", "esno": "^0.16.3", "execa": "^4.1.0", "fs-extra": "^10.1.0", "hash-sum": "^2.0.0", "minimist": "^1.2.6", "picocolors": "^1.0.0", "prettier": "^2.7.1", "puppeteer": "^14.4.0", "rollup": "^2.75.6", "semver": "^7.3.7", "slash": "^3.0.0", "source-map": "^0.6.1", "unbuild": "^0.7.4", "vite": "^3.0.0", "vitest": "^0.15.1", "vue": "^2.7.0-beta.8"}, "scripts": {"dev": "unbuild --stub", "build": "unbuild && esno scripts/patchCJS.ts", "test": "vitest run", "release": "node scripts/release.js", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}}