<template>
  <div id="app">
    <div class="app-container">
      <!-- 页面头部 -->
      <header class="header">
        <h1>Vue2 + ECharts 地图下钻功能</h1>
        <p>基于ECharts和GeoJSON数据的中国地图可视化组件</p>
      </header>

      <div class="main-content">
        <!-- 功能特性展示 -->
        <div class="features">
          <div class="feature-card">
            <div class="feature-icon">🗺️</div>
            <div class="feature-title">多层级下钻</div>
            <div class="feature-desc">支持全国→省份→城市→县区的多层级地图下钻浏览</div>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎯</div>
            <div class="feature-title">热力图展示</div>
            <div class="feature-desc">集成热力图和特效散点图，直观展示数据分布</div>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🧭</div>
            <div class="feature-title">面包屑导航</div>
            <div class="feature-desc">提供清晰的导航路径，支持快速跳转到任意层级</div>
          </div>
          <div class="feature-card">
            <div class="feature-icon">⚡</div>
            <div class="feature-title">高性能渲染</div>
            <div class="feature-desc">基于ECharts Canvas渲染，支持大数据量流畅交互</div>
          </div>
        </div>

        <!-- 地图组件区域 -->
        <div class="map-section">
          <h2 class="section-title">交互式地图</h2>
          
          <!-- 使用说明 -->
          <div class="instructions">
            <h3>使用说明：</h3>
            <ul>
              <li>点击地图上的省份区域可以下钻到该省份的详细地图</li>
              <li>使用左上角的面包屑导航可以快速返回到上级地图</li>
              <li>点击"返回"按钮可以逐级返回上一层地图</li>
              <li>支持鼠标滚轮缩放和拖拽平移操作</li>
              <li>悬停在地图区域上可以查看详细信息</li>
            </ul>
          </div>

          <!-- 地图组件 -->
          <MapDrillDown />
        </div>
      </div>

      <!-- 页面底部 -->
      <footer class="footer">
        <p>© 2024 Vue2 + ECharts 地图下钻功能演示 | 基于开源GeoJSON数据</p>
      </footer>
    </div>
  </div>
</template>

<script>
import MapDrillDown from './MapDrillDown.vue';

export default {
  name: 'App',
  components: {
    MapDrillDown
  },
  data() {
    return {
      message: 'Vue2 + ECharts 地图下钻功能演示'
    };
  },
  mounted() {
    console.log('Vue2 + ECharts 地图下钻应用已启动');
  }
};
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333;
}

.app-container {
  min-height: 100vh;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 300;
}

.header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.main-content {
  max-width: 1400px;
  margin: 0 auto;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.feature-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 10px;
  color: #667eea;
}

.feature-title {
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.feature-desc {
  color: #666;
  line-height: 1.5;
}

.map-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.instructions {
  background: #f8f9fa;
  border-left: 4px solid #667eea;
  padding: 15px 20px;
  margin-bottom: 20px;
  border-radius: 0 8px 8px 0;
}

.instructions h3 {
  color: #333;
  margin-bottom: 10px;
}

.instructions ul {
  list-style: none;
  padding-left: 0;
}

.instructions li {
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.instructions li:before {
  content: "•";
  color: #667eea;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.footer {
  text-align: center;
  margin-top: 40px;
  padding: 20px;
  color: #666;
  border-top: 1px solid #eee;
}

@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .features {
    grid-template-columns: 1fr;
  }
}
</style>
